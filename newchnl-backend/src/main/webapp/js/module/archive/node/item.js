var _newcolumn = {
    type: "newcolumn"
};
/** ************************************** */
/** INPUT 区域 */
/** ************************************** */
var _parentEntityName = {
    type: "input",
    name: "parentEntityName",
    label: "分公司：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _parentEntityNameChange = {
    type: "input",
    name: "parentEntityNameChange",
    label: "分公司：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _parentEntityNameChangeId = {
    type: "input",
    name: "parentEntityNameChangeId",
    label: "分公司ID：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true,
    hidden: true
};
var _parentEntityName_1 = {
    type: "input",
    name: "parentEntityName",
    label: "代理商：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};

//归属网格
var _gridName = {
    type: "input",
    name: "gridName",
    label: "归属网格：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    hidden: true
};

var _streetId= {
    type: "input",
    name: "streetId",
    label: "归属网格ID：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    hidden: true
};

//归属网格
var _gridTree = {
    type: "gridTree",
    name: "gridInfo",
    label: "归属网格：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
//归属网格(非必填)
var _gridTreeNullable = {
    type: "gridTree",
    name: "gridInfo",
    label: "归属网格：",
    width: 140,
    offsetTop: 10,
    readonly: true
};

var _internetWeb = {
    type: "input",
    name: "internetWeb",
    label: "互联网推广网址：",
    width: 140,
    offsetTop: 10,
};
var _channelEntitySerial = {
    type: "input",
    name: "channelEntitySerial",
    label: "门店编码：",
    width: 140,
    offsetTop: 10,
    readonly: true
};
var _channelEntityName = {
    type: "input",
    name: "channelEntityName",
    label: "自助终端名称：",
    offsetTop: 10,
    maxLength: 64,
    width: 140,
    required: true,
    validate: "submitEntityName"
};
var _channelEntityName_1 = {
    type: "input",
    name: "channelEntityName",
    label: "门店名称：",
    offsetTop: 10,
    maxLength: 64,
    width: 140,
    required: true,
    validate: "submitEntityName"
};
var _opChannelNodeName = {
    type: "combo",
    name: "channelEntityName",
    label: "门店名称：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    note: {
        text: '请从下拉框中选择一项'
    },
    required: true,
    validate: "submitEntityName"
};
var _channelEntityName_2 = {
    type: "input",
    name: "channelEntityName",
    label: "直销队员名称：",
    maxLength: 64,
    offsetTop: 10,
    width: 140,
    required: true,
    validate: "submitEntityName"
};
var _schoolName = {
    type: "input",
    name: "schoolName",
    label: "学校名称：",
    offsetTop: 10,
    maxLength: 25,
    width: 140,
    required: true
};
var _nodeAddr = {
    type: "input",
    name: "nodeAddr",
    label: "布设地址：",
    width: 405,
    maxLength: 125,
    offsetTop: 10
};
var _nodeAddr_1 = {
    type: "input",
    name: "nodeAddr",
    label: "门店地址：",
    required: true,
    maxLength: 125,
    width: 405,
    offsetTop: 10,
    validate: "submitNodeAddr"
};

var _nodeCitySchool = {
    type: "combo",
    name: "nodeCity",
    readonly: true,
    label: "学校地址：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _nodeAreaSchool = {
    type: "combo",
    name: "nodeArea",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _nodeCityBS = {
    type: "combo",
    name: "nodeCity",
    readonly: true,
    label: "布设地址：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _nodeAreaBS = {
    type: "combo",
    name: "nodeArea",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _nodeCity = {
    type: "combo",
    name: "nodeCity",
    readonly: true,
    label: "门店地址：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _nodeArea = {
    type: "combo",
    name: "nodeArea",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _nodeAddrees = {
    type: "input",
    name: "nodeAddrees",
    required: true,
    maxLength: 125,
    width: 405,
    offsetTop: 10,
    validate: "submitNodeAddr"
};
var _nodeAddr_2 = {
    type: "input",
    name: "nodeAddr",
    label: "学校地址：",
    required: true,
    maxLength: 125,
    width: 405,
    offsetTop: 10
};
var _whiteCardLimit = {
    type: "input",
    name: "whiteCardLimit",
    label: "每月空选白卡申领额度：",
    width: 140,
    maxLength: 4,
    offsetTop: 10,
    validate: "checkIsDigitalAndLength"
};
var _buildingAmount = {
    type: "input",
    name: "buildingAmount",
    label: "设备投资总额：",
    maxLength: 12,
    width: 140,
    offsetTop: 10,
    validate: "ValidHouseRentAmount"
};
var _eqmtAmount = {
    type: "input",
    name: "eqmtAmount",
    label: "装修投资总额：",
    width: 140,
    maxLength: 12,
    offsetTop: 10,
    validate: "ValidHouseRentAmount"
};
var _officesAmount = {
    type: "input",
    name: "officesAmount",
    label: "办公和营业家具投资总额：",
    width: 140,
    maxLength: 20,
    offsetTop: 10,
    validate: "ValidHouseRentAmount"
};
var _czcardLimit = {
    type: "input",
    name: "czcardLimit",
    label: "月冲值卡额度：",
    maxLength: 10,
    width: 140,
    offsetTop: 10,
    validate: "checkIsDigitalAndLength"
};
var _yhcardLimit = {
    type: "input",
    name: "yhcardLimit",
    label: "月有号卡额度：",
    maxLength: 10,
    width: 140,
    offsetTop: 10,
    validate: "checkIsDigitalAndLength"
};
var _nodeLicenseId = {
    type: "input",
    name: "nodeLicenseId",
    label: "门店工商号：",
    maxLength: 30,
    width: 140,
    required: true,
    offsetTop: 10
};
var _unityLicenseName = {
    type: "input",
    name: "unityLicenseName",
    label: "个体工商户名称：",
    maxLength: 15,
    width: 140,
    offsetTop: 10
};

// -----------------------------------------//
var _postCode = {
    type: "input",
    name: "postCode",
    label: "门店邮编：",
    maxLength: 6,
    width: 140,
    offsetTop: 10,
    required: true,
    validate: "validPostCode"
};

function validPostCode(data) {
    data = data + "";
    var reg = /[1-9]\d{5}/;
    return reg.test(data) && data.length == 6;
}

// -----------------------------------------//

var _longitude = {
    type: "input",
    name: "longitude",
    label: "经度°：",
    maxLength: 50,
    width: 140,
    offsetTop: 10,
    validate: "validLongitude"
};
function validLongitude(data) {
    data = data + "";
    var reg1 = /^\d*$/;
    var reg2 = /^(-)?[1-9]\d{0,2}(\.\d{1,6})?$|^0(\.\d{6})?$/;
    var reg3 = /(?:[0-9]|[1-9][0-9]|1[0-7][0-9]|180).([0-9]{6})/;
    if (reg1.test(data)) {
        dhtmlx.alert({title: "警告", text: "经度不能为整数，请重新输入", type: "alert-error"});
        return false;
    }
    if(!reg2.test(data)){
            dhtmlx.alert({title: "警告", text: "经度最多只能支持3位整数+6位小数，请重新输入", type: "alert-error"});
            return false;

    }
    if (!reg3.test(data)){
        dhtmlx.alert({title: "警告", text: "经度最多只能支持3位整数+6位小数，请重新输入", type: "alert-error"});
        return false;
    }
    return true;
}
function checkSumArea(data) {
    var reg = /^\d+(\.\d{1,2})?$/;
        if(!reg.test(data) || !data>0){
            dhtmlx.alert({title: "警告", text: "营业面积只能支持大于0的两位小数，请重新输入", type: "alert-error"});
            return false;
        }
        return true;
}
var _latitude = {
    type: "input",
    name: "latitude",
    label: "纬度°：",
    maxLength: 50,
    width: 140,
    offsetTop: 10,
    validate: "validLatitude"
};
function validLatitude(data) {
    data = data + "";
    var reg1 = /^\d*$/;
    var reg2 = /^(-)?[1-9]\d{0,1}(\.\d{1,6})?$|^0(\.\d{6})?$/;
    var reg3 = /(?:[0-9]|[1-8][0-9]|90).([0-9]{6})/ ; // 纬度正则
    if (reg1.test(data)) {
        dhtmlx.alert({title: "警告", text: "纬度不能为整数，请重新输入", type: "alert-error"});
        return false;
    }
    if(!reg2.test(data)){
            dhtmlx.alert({title: "警告", text: "纬度最多只能支持2位整数+6位小数，请重新输入", type: "alert-error"});
            return false;
    }
    if (!reg3.test(data)){
        dhtmlx.alert({title: "警告", text: "纬度最多只能支持2位整数+6位小数，请重新输入", type: "alert-error"});
        return false;
    }
    return true;

}
var _useArea = {
    type: "input",
    name: "useArea",
    label: "营业面积：",
    maxLength: 20,
    width: 140,
    offsetTop: 10,
    validate: "checkIsDigitalAndLength"
};
var _staffNum = {
    type: "input",
    name: "staffNum",
    label: "营业厅实际员工数量：",
    maxLength: 4,
    width: 140,
    offsetTop: 10,
    required: true,
    validate: "checkIsDigitalAndLength"
};
var _staffNumA = {
    type: "input",
    name: "staffNumA",
    label: "其中A类员工数量：",
    maxLength: 4,
    width: 140,
    offsetTop: 10,
    required: true,
    validate: "checkIsDigitalAndLength"
};
var _staffNumC = {
    type: "input",
    name: "staffNumC",
    label: "其中C类员工数量：",
    maxLength: 4,
    width: 140,
    offsetTop: 10,
    required: true,
    validate: "checkIsDigitalAndLength"
};

var _rel_relationName_1 = {
    type: "input",
    name: "rel_relationName_1",
    label: "负责人名称：",
    maxLength: 10,
    required: true,
    width: 140,
    offsetTop: 10
};

var _rel_relationName_2 = {
    type: "input",
    name: "rel_relationName_1",
    label: "厅经理：",
    maxLength: 10,
    required: true,
    width: 140,
    offsetTop: 10
};

// -----------手机号码检验---------------//
function phone(data) {
    data = data + "";
    var reg = /1[3-9]\d{9}/;
    return reg.test(data) && data.length == 11;
}

// ---------------------------------//

var _rel_relationMobile_1 = {
    type: "input",
    name: "rel_relationMobile_1",
    label: "负责人手机：",
    required: true,
    width: 140,
    offsetTop: 10,
    validate: "phone"
};

var _rel_relationMobile_2 = {
    type: "input",
    name: "rel_relationMobile_1",
    label: "厅经理联系电话：",
    required: true,
    width: 140,
    offsetTop: 10,
    validate: "phone"
};

function ValidPhone(data) {
    var reg = /^(\d{8}|\d{11})$/;
    return reg.test(data);
}

var _foreignContactNumber = {
    type: "input",
    name: "foreignContactNumber",
    label: "对外联系电话：",
    required: true,
    width: 140,
    offsetTop: 10,
    validate: "ValidPhone"
};

var _internalContactNumber = {
    type: "input",
    name: "internalContactNumber",
    label: "对内联系电话：",
    required: true,
    width: 140,
    offsetTop: 10,
    validate: "ValidPhone"
};

var _rel_email_1 = {
    type: "input",
    name: "rel_email_1",
    label: "负责人邮箱：",
    required: true,
    width: 140,
    offsetTop: 10,
    validate: "ValidEmail"
};
var _rel_relationName_6 = {
    type: "input",
    name: "rel_relationName_6",
    label: "分管督导：",
    required: true,
    maxLength: 10,
    width: 140,
    offsetTop: 10
};
var _rel_relationMobile_6 = {
    type: "input",
    name: "rel_relationMobile_6",
    label: "督导联系电话：",
    required: true,
    width: 140,
    offsetTop: 10,
    validate: "phone"
};
var _rel_email_6 = {
    type: "input",
    name: "rel_email_6",
    label: "督导邮箱：",
    width: 140,
    required: true,
    offsetTop: 10,
    validate: "ValidEmail"
};

// -----------移动号码--------------//
function cmPhone(data) {
    data = data + "";
    var reg1 = /(134|135|136|137|138|139|147|150|151|152|153|154|155|156|157|158|159|172|178|182|183|184|185|187|188|189|198|195)\d{8}/;
    var reg2 = /(134|135|136|137|138|139|147|150|151|152|153|154|155|156|157|158|159|172|178|182|183|184|185|187|188|189|198|195)\d{8}-\d{4}/;
    return (reg1.test(data) && data.length == 11)
        || (reg2.test(data) && data.length == 16);
}

// ------------------------------//

var _rel_relationMobile_9 = {
    type: "input",
    name: "rel_relationMobile_9",
    label: "指定服务号码1：",
    width: 140,
    offsetTop: 10,
    validate: "cmPhone"
};
var _rel_ext2_9 = {
    type: "input",
    name: "rel_otherContact_9",
    label: "指定服务号码2：",
    width: 140,
    offsetTop: 10,
    validate: "cmPhone"
};
var _circleName = {
    type: "input",
    name: "circleName",
    label: "商圈名称：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _centralName = {
    type: "input",
    name: "centralName",
    label: "集中地名称：",
    width: 405,
    offsetTop: 10,
    required: true,
    readonly: true
};
var _centralPosition = {
    type: "input",
    name: "centralPosition",
    label: "集中地位置描述：",
    width: 405,
    offsetTop: 10,
    required: true,
    readonly: true
};

// -------------------------------//
function authoriztion(data) {
    data = data + "";
    if (data.length != 15) {
        return false;
    }
    var one2four = data.substring(1, 4);
    if ("210" != one2four) {
        return false;
    }
    var five2eight = new Number(data.substring(5, 8));
    if (five2eight > 119 && five2eight < 139) {
        return false;
    }
    return true;
}

var _nodeAuthoriztionId = {
    type: "input",
    name: "nodeAuthoriztionId",
    label: "授权编码：",
    width: 140,
    offsetTop: 10,
    required: false,
    validate: "authoriztion",
    note: {
        text: "#210#(000-119|139-999)#######"
    }
};
// -------------------------------//

var _channelEntityId = {
    type: "input",
    name: "channelEntityId",
    label: "渠道编码：",
    required: true,
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _regMobile = {
    type: "input",
    name: "regMobile",
    label: "空中选号注册号码：",
    width: 140,
    offsetTop: 10
};
var _pauseReason = {
    type: "input",
    name: "pauseReason",
    label: "暂停（关闭）原因：",
    width: 405,
    maxLength: 250,
    offsetTop: 10,
    required: true,
    validate: "checkPauseReason"
};

var _areaName = {
    type: "input",
    name: "areaName",
    label: "小区名字：",
    width: 140,
    offsetTop: 10
};
/** ************************************** */
/** COMBO 区域 */
/** ************************************** */
var _nodeKind = {
    type: "combo",
    name: "nodeKind",
    label: "四级渠道分类：",
    readonly: true,
    width: 140,
    offsetLeft: 60,
    offsetTop: 40,
    note: {
        text: '请从下拉框中选择一项'
    },
    required: true
};

var _channelClassFirst = {
    type: "combo",
    name: "channelClassFirst",
    label: "一级渠道分类",
    width: 140,
    offsetLeft: 60,
    offsetTop: 40,
    readonly: true,
    required: true
};
var _channelClassSecond = {
    type: "combo",
    name: "channelClassSecond",
    label: "二级渠道分类",
    width: 140,
    offsetLeft: 60,
    offsetTop: 40,
    readonly: true,
    required: true
};
var _channelClassThird = {
    type: "combo",
    name: "channelClassThird",
    label: "三级渠道分类",
    width: 140,
    offsetLeft: 60,
    offsetTop: 40,
    readonly: true,
    required: true
};
var _nodeKind_1 = {
    type: "combo",
    name: "nodeKind",
    label: "四级渠道分类：",
    width: 140,
    readonly: true,
    offsetTop: 10
};


var _channelClassFirst_1 = {
    type: "combo",
    name: "channelClassFirst",
    label: "一级渠道分类",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _channelClassSecond_1 = {
    type: "combo",
    name: "channelClassSecond",
    label: "二级渠道分类",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _channelClassThird_1 = {
    type: "combo",
    name: "channelClassThird",
    label: "三级渠道分类",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _nodeGrade = {
    type: "combo",
    name: "nodeGrade",
    label: "网点等级：",
    width: 140,
    readonly: true,
    required: true,
    offsetTop: 10
};
var _districtId = {
    type: "combo",
    name: "districtId",
    label: "归属区域：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _regionId = {
    type: "combo",
    name: "regionId",
    label: "行政区：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _channelEntityStatus = {
    type: "combo",
    name: "channelEntityStatus",
    label: "门店状态：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _trustees = {
    type: "combo",
    name: "trustees",
    label: "托管方：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _trusteesZhi = {
    type: "combo",
    name: "trustees",
    label: "直供代理方：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _placeManner = {
    type: "combo",
    name: "placeManner",
    label: "布放方式：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _systemConnection = {
    type: "combo",
    name: "systemConnection",
    label: "系统接入：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _beneficialScan = {
    type: "combo",
    name: "beneficialScan",
    label: "实名制鉴别仪扫描：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _gztCheck = {
    type: "combo",
    name: "gztCheck",
    label: "实名制国政通校验：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _settingPointFeature = {
    type: "combo",
    name: "settingPointFeature",
    label: "布设点特征：",
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _simSaleMonthly = {
    type: "combo",
    name: "simSaleMonthly",
    label: "月号码销量：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10,
    validate: "checkIsDigitalAndLength"
};
var _cellSaleMonthly = {
    type: "combo",
    name: "cellSaleMonthly",
    label: "月手机销量：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10,
    validate: "checkIsDigitalAndLength"
};
var _g3g4CellSaleMonthly = {
    type: "combo",
    name: "g3g4CellSaleMonthly",
    label: "月3G/4G手机销量：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10,
    validate: "checkIsDigitalAndLength"
};
var _addressType = {
    type: "combo",
    name: "addressType",
    label: "城乡级别：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _areaShape = {
    type: "combo",
    name: "areaShape",
    label: "区域形态：",
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _isExclusive = {
    type: "combo",
    name: "isExclusive",
    label: "是否排他：",
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _accreditExt4 = {
    type: "combo",
    name: "accreditExt4",
    label: "是否授权网点：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _isAuthorized = {
    type: "combo",
    name: "isAuthorized",
    label: "是否异业授权：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _nodeLevel = {
    type: "combo",
    name: "nodeLevel",
    label: "网点星级：",
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _isCircle = {
    type: "combo",
    name: "isCircle",
    label: "是否在商圈内：",
    required: true,
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _isCentral = {
    type: "combo",
    name: "isCentral",
    label: "是否在集中地：",
    required: true,
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _teamType = {
    type: "combo",
    name: "teamType",
    label: "直销队员状态：",
    required: true,
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _nodeType = {
    type: "combo",
    name: "nodeType",
    label: "五级渠道分类：",
    required: true,
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _circleLevel = {
    type: "combo",
    name: "circleLevel",
    label: "商圈级别：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _circlePlace = {
    type: "combo",
    name: "circlePlace",
    label: "商圈内位置：",
    required: true,
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _isNetwork = {
    type: "combo",
    name: "isNetwork",
    label: "是否联网：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _storeType = {
    type: "combo",
    name: "storeType",
    label: "门店类型：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _storesType = {
    type: "combo",
    name: "storesType",
    label: "门店类型：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _houseAppraisalUnitPrice = {
    type: "input",
    name: "houseAppraisalUnitPrice",
    label: "房屋评估单价：",
    required: true,
    width: 140,
    offsetTop: 10,
    validate: "ValidhouseAppraisalUnitPrice"
};

function ValidhouseAppraisalUnitPrice(data) {
    var reg = /^\d+(\.\d{1,2})?$/;
    return reg.test(data);
}

var _cooperativeAreaOfHousing = {
    type: "input",
    name: "cooperativeAreaOfHousing",
    label: "房屋合作面积:",
    required: true,
    width: 140,
    offsetTop: 10,
    validate: "ValidCooperativeAreaOfHousing"
};
var _isRLXS = {
    type: "combo",
    name: "isRLXS",
    label: "是否顺差让利销售厅店：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _isRLXS = {
    type: "combo",
    name: "isRLXS",
    label: "是否顺差让利销售厅店：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};

var _is5G = {
    type: "combo",
    name: "is5G",
    label: "是否5G厅：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};

var _isThirdBusi = {
    type: "combo",
    name: "isThirdBusi",
    label: "是否第三方直销：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};

var _busiRole = {
    type: "combo",
    name: "busiRole",
    label: "角色：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _isChannelNationalSubsidy = {
    type: "combo",
    name: "isChannelNationalSubsidy",
    label: "是否是国补入围渠道：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};

var _isChannelNationalSubsidy1 = {
    type: "combo",
    name: "isChannelNationalSubsidy",
    label: "是否是国补入围渠道：",
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _isStrategicCooperation = {
    type: "combo",
    name: "isStrategicCooperation",
    label: "是否战略合作：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _strategicCooperationType = {
    type: "combo",
    name: "strategicCooperationType",
    label: "战略合作类型：",
    readonly: true,
    width: 140,
    offsetTop: 10
};
function ValidCooperativeAreaOfHousing(data) {
    var reg = /^\d+(\.\d{1,2})?$/;
    return reg.test(data);
}

var _pcWriteCard = {
    type: "combo",
    name: "pcWriteCard",
    label: "PC写卡：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _distributionAttribute = {
    type: "combo",
    name: "distributionAttribute",
    label: "连锁分销属性：",
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _storeAttribute = {
    type: "combo",
    name: "storeAttribute",
    label: "手机卖场属性：",
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _isShoppe = {
    type: "combo",
    name: "isShoppe",
    label: "是否专区专柜：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _shoppeType = {
    type: "combo",
    name: "shoppeType",
    label: "专区专柜类型：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _netType = {
    type: "combo",
    name: "netType",
    label: "联网方式：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _nodeStep = {
    type: "combo",
    name: "nodeStep",
    label: "门店台阶：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _joinType = {
    type: "combo",
    name: "joinType",
    label: "合作方式：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    hidden: true
};
var _isImageEquipment = {
    type: "combo",
    name: "isImageEquipment",
    label: "实名影像采集设备：",
    required: true,
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _isICompany = {
    type: "combo",
    name: "isICompany",
    label: "是否我司：",
    readonly: true,
    width: 140,
    offsetTop: 10
};
/** ************************************** */
/** HIDDEN 隐藏区域 */
/** ************************************** */
var _parentEntity = {
    type: "hidden",
    name: "parentEntity",
    width: 140,
    offsetTop: 10
};
var _circleId = {
    type: "hidden",
    name: "circleId",
    label: "商圈ID：",
    width: 140,
    offsetTop: 10
};
var _centralId = {
    type: "hidden",
    name: "centralId",
    label: "集中地ID：",
    width: 140,
    offsetTop: 10
};
var _rel_relationType_1 = {
    type: "hidden",
    name: "rel_relationType_1",
    required: true,
    width: 140,
    value: '1',
    offsetTop: 10
};
var _rel_relationType_6 = {
    type: "hidden",
    name: "rel_relationType_6",
    label: "网点督导：",
    required: true,
    width: 140,
    value: '6',
    offsetTop: 10
};
var _rel_relationType_9 = {
    type: "hidden",
    name: "rel_relationType_9",
    label: "指定服务号码：",
    required: true,
    width: 140,
    value: '9',
    offsetTop: 10
};
var _nodeKind_2 = {
    type: "hidden",
    name: "nodeKind",
    width: 140,
    offsetTop: 10
};
/** ************************************** */
/** BUTTON 隐藏区域 */
/** ************************************** */
var _confirmBtn = {
    type: "button",
    name: "confirmBtn",
    width: 170,
    value: "确定",
    offsetTop:20,
    offsetLeft: 150
};
var _chooseCircle = {
    type: "button",
    name: "chooseCircle",
    value: "选择商圈",
    offsetLeft: 60,
    offsetTop: 4
};
var _searchArea = {
    type: "button",
    name: "searchArea",
    value: "搜索小区",
    offsetLeft: 60,
    offsetTop: 4
};
var _addArea = {
    type: "button",
    name: "addArea",
    value: "增加小区",
    offsetLeft: 120,
    offsetTop: 4
};
var _delArea = {
    type: "button",
    name: "delArea",
    value: "删除小区",
    offsetLeft: 240,
    offsetTop: 4
};
var _chooseCentral = {
    type: "button",
    name: "chooseCentral",
    value: "选择集中地",
    offsetLeft: 60,
    offsetTop: 4
};
var _saveBtn = {
    type: "button",
    name: "saveBtn",
    value: "保存",
    offsetLeft: 340
};
var _cancelBtn = {
    type: "button",
    name: "cancelBtn",
    value: "取消",
    offsetLeft: 50
};
var _updateChange = {
    type: "button",
    name: "updateChange",
    value: "营业厅变更性质/变更代理商",
    offsetLeft: 50
};
var _updateChange1 = {
    type: "button",
    name: "updateChange1",
    value: "变更为直营厅",
    offsetLeft: 50
};
var _updateChange2 = {
    type: "button",
    name: "updateChange2",
    value: "营业厅变更代理商",
    offsetLeft: 50
};
var _closeBtn = {
    type: "button",
    name: "closeBtn",
    value: "关闭",
    width: 50,
    offsetLeft: 780,
    offsetTop: 10
};

/** ************************************** */
/** BUTTON 日期区域 */
/** ************************************** */
var _signBeginDate = {
    type: "calendar",
    name: "signBeginDate",
    label: "签约时间：",
    width: 140,
    offsetTop: 10,
    readonly: true
};
var _signEndDate = {
    type: "calendar",
    name: "signEndDate",
    label: "协议截止时间：",
    width: 140,
    offsetTop: 10,
    readonly: true
};

var _businessStartDate = {
    type: "calendar",
    name: "businessStartDate",
    label: "开业时间：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};

var _lastDecorateTime = {
    type: "calendar",
    name: "lastDecorateTime",
    label: "最近一次整体装修时间：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _busiTimesAvg = {
    type: "input",
    name: "busiTimesAvg",
    label: "每天营业时长：",
    width: 140,
    maxLength: 128,
    offsetTop: 10,
    required: true
//    validate : "ValidBusiTimesAvgAmount"
};
var _isBusiTimeGt25 = {
    type: "combo",
    name: "isBusiTimeGt25",
    label: "每月营业时长是否大于25天：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _hasOfferGetGoods = {
    type: "combo",
    name: "hasOfferGetGoods",
    label: "是否提供到店取货：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _hasTerminalSell = {
    type: "combo",
    name: "hasTerminalSell",
    label: "是否支持终端销售：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _zdtAmount = {
    type: "input",
    name: "zdtAmount",
    label: "中岛台数量：",
    width: 140,
    offsetTop: 10,
    maxLength: 8,
    required: true,
    validate: "ValidHouseRentAmount"
};
var _isYsrg = {
    type: "combo",
    name: "isYsrg",
    label: "是否引商入柜：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _statusChangeHis = {
    type: "input",
    name: "statusChangeHis",
    label: "营业厅状态年度变化情况：",
    width: 140,
    offsetTop: 10,
    rows: 3
};
var _doneDate = {
    type: "input",
    name: "doneDate",
    label: "最后更新时间：",
    width: 140,
    offsetTop: 10
};
var _parentOrgName = {
    type: "input",
    name: "parentOrgName",
    label: "分公司：",
    width: 140,
    offsetTop: 10
};
var _guaranteeFee = {
    type: "input",
    name: "guaranteeFee",
    label: "保证金（元）：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _hasQueueMachine = {
    type: "combo",
    name: "hasQueueMachine",
    label: "有无排队叫号机：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _hasPosMachine = {
    type: "combo",
    name: "hasPosMachine",
    label: "有无POS机：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _selfHelpAmount = {
    type: "input",
    name: "selfHelpAmount",
    label: "综合性自助终端台数：",
    width: 140,
    offsetTop: 10,
    maxLength: 8,
    required: true,
    validate: "ValidHouseRentAmount"
};
var _tvScreenAmount = {
    type: "input",
    name: "tvScreenAmount",
    label: "电视屏个数：",
    width: 140,
    offsetTop: 10,
    maxLength: 8,
    required: true,
    validate: "ValidHouseRentAmount"
};
var _hasChooseMachine = {
    type: "combo",
    name: "hasChooseMachine",
    label: "有无选号机：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _selectDockingSystem = {
    type: "combo",
    name: "selectDockingSystem",
    label: "选择对接系统：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _isZxqh= {
    type: "combo",
    name: "isZxqh",
    label: "是否开放在线取号：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _isYyqh= {
    type: "combo",
    name: "isYyqh",
    label: "是否开放预约取号：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _isEnterGrid= {
    type: "combo",
    name: "isEnterGrid",
    label: "是否入网格：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _zxqhSetting= {
    type: "input",
    name: "zxqhSetting",
    label: "在线取号链接配置：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _yyqhSetting= {
    type: "input",
    name: "yyqhSetting",
    label: "预约取号链接配置：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _agentName = {
    type: "input",
    name: "agentName",
    label: "合作方简称：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _agentNameChange = {
    type: "input",
    name: "agentName",
    label: "合作方简称：",
    width: 140,
    offsetTop: 10,
    required: true,
    readonly: true
};
var _agentFullName = {
    type: "input",
    name: "agentFullName",
    label: "合作方全称：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _contractNumber = {
    type: "input",
    name: "contractNumber",
    required: true,
    readonly: true,
    label: "合同编号：",
    width: 140,
    offsetTop: 10
};
var _agentSignBeginDate = {
    type: "input",
    name: "agentSignBeginDate",
    label: "协议开始时间：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _agentSignEndDate = {
    type: "input",
    name: "agentSignEndDate",
    label: "协议截止时间：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _agentRelationName = {
    type: "input",
    name: "agentRelationName",
    label: "联系人名称：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _agentRelationMobile = {
    type: "input",
    name: "agentRelationMobile",
    label: "联系人电话：",
    width: 140,
    offsetTop: 10,
    required: true
};

var _businessTime = {
    type: "businessHours",
    name: "businessTime",
    label: "营业时间：",
    readonly: true,
    required: true,
    inputWidth: 405
};

var _isStore = {
    type: "combo",
    name: "isStore",
    label: "是否含卖场：",
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _sumArea = {
    type: "input",
    name: "sumArea",
    label: "营业面积(㎡)：",
    width: 140,
    maxLength: 12,
    offsetTop: 10,
    required: true,
    validate: "checkSumArea"
};
var _frontUseArea = {
    type: "input",
    name: "frontUseArea",
    label: "前台面积(㎡)：",
    width: 140,
    maxLength: 12,
    offsetTop: 10,
    required: true,
    validate: "ValidHouseRentAmount"
};

var _isPropertyCard = {
    type: "combo",
    name: "isPropertyCard",
    label: "是否有房产证：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};

var _propertyId = {
    type: "input",
    name: "propertyId",
    label: "房产证号：",
    width: 140,
    maxLength: 30,
    offsetTop: 10,
    required: true
};

var _propertyNature = {
    type: "combo",
    name: "propertyNature",
    label: "房产性质：",
    width: 140,
    offsetTop: 10,
    readonly: true
};

var _houseAmount = {
    type: "input",
    name: "houseAmount",
    label: "购房金额（元）：",
    width: 140,
    maxLength: 12,
    offsetTop: 10,
    required: true,
    validate: "ValidHouseRentAmount"
};
var _purchaseDate = {
    type: "calendar",
    name: "purchaseDate",
    label: "购买时间：",
    width: 140,
    offsetTop: 10,
    required: true,
    readonly: true
};

var _rentStartDate = {
    type: "calendar",
    name: "rentStartDate",
    label: "租赁起始时间：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};

var _rentEndDate = {
    type: "calendar",
    name: "rentEndDate",
    label: "租赁截止时间：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};

function ValidHouseRentAmount(data) {
    var reg = /^\d{0,}$/;
    return reg.test(data);
}

function ValidBusiTimesAvgAmount(data) {
    var reg = /(^\d{0,}$)|(^(\d{0,}).(\d{1})$)/;
    return reg.test(data);
}

var _houseRentAmount = {
    type: "input",
    name: "houseRentAmount",
    label: "租金（月/元）：",
    width: 140,
    offsetTop: 10,
    required: true,
    validate: "ValidHouseRentAmount"
};
var _compileNum = {
    type: "input",
    name: "compileNum",
    label: "营业厅编制员工数量：",
    width: 140,
    offsetTop: 10,
    required: true,
    validate: "checkIsDigitalAndLength"
};

var _empSafeNums = {
    type: "input",
    name: "empSafeNums",
    label: "保安人数：",
    width: 140,
    offsetTop: 10,
    validate: "checkIsDigitalAndLength"
};

var _empClearNums = {
    type: "input",
    name: "empClearNums",
    label: "保洁人数：",
    width: 140,
    offsetTop: 10,
    validate: "checkIsDigitalAndLength"
};
var _physicsNum = {
    type: "input",
    name: "physicsNum",
    label: "物理台席数：",
    width: 140,
    offsetTop: 10,
    required: true,
    validate: "checkIsDigitalAndLength"
};
var _seatNum = {
    type: "input",
    name: "seatNum",
    label: "实际台席数：",
    width: 140,
    offsetTop: 10,
    required: true,
    validate: "checkIsDigitalAndLength"
};

var _isVip = {
    type: "combo",
    name: "isVip",
    label: "有无VIP室：",
    width: 140,
    offsetTop: 10,
    required: true,
    readonly: true
};

var _unifyCode = {
    type: "input",
    name: "unifyCode",
    label: "全网统一编码：",
    width: 140,
    offsetTop: 10
};

var _fullName = {
    type: "input",
    name: "fullName",
    label: "支撑合作方：",
    width: 140,
    offsetTop: 10
};

var _supportAgentName = {
    type: "input",
    name: "supportAgentName",
    label: "支撑合作方：",
    width: 140,
    offsetTop: 10
};

var _supportCategory = {
    type: "combo",
    name: "supportCategory",
    label: "支撑类别：",
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _systemConnectionType = {
    type: "hidden",
    name: "systemConnectionType",
    label: "系统接入类型：",
    width: 140,
    offsetTop: 10
};
var _A = {
    type: "checkbox",
    name: "A",
    label: "终端销售：",
    offsetTop: 10,
    labelWidth: 105
};
var _B = {
    type: "checkbox",
    name: "B",
    label: "PC写卡：",
    offsetTop: 10,
    labelWidth: 105
};
var _C = {
    type: "checkbox",
    name: "C",
    label: "空选机写卡：",
    offsetTop: 10,
    labelWidth: 105
};
var _D = {
    type: "checkbox",
    name: "D",
    label: "宽带销售：",
    offsetTop: 10,
    labelWidth: 105
};
var _E = {
    type: "checkbox",
    name: "E",
    label: "小移微店：",
    offsetTop: 10,
    labelWidth: 105
};
var _grantPauthorizationLable = {
    type: "label",
    name: "grantPauthorizationLable",
    label: "授权业务范围："
};

var _grantPauthorization = {
    type: "hidden",
    name: "grantPauthorization",
    label: "授权业务范围：",
    width: 140,
    offsetTop: 10
};

var _grantPauthorization1 = {
    type: "checkbox",
    name: "grantPauthorization1",
    label: "全国移动电话卡",
    offsetTop: 10,
    labelWidth: 20
};
var _grantPauthorization2 = {
    type: "checkbox",
    name: "grantPauthorization2",
    label: "全国宽带",
    offsetTop: 10,
    labelWidth: 20
};
var _grantPauthorization99 = {
    type: "checkbox",
    name: "grantPauthorization99",
    label: "其他",
    offsetTop: 10,
    labelWidth: 20
};

var _grantPauthorizations = {
    type: "input",
    name: "grantPauthorizations",
    label: "授权业务范围(其他补充)：",
    width: 400,
    offsetTop: 10,
    hidden:true,
    maxLength:512
};

var _eleChannelNodeType={
    type: "combo",
    name: "eleChannelNodeType",
    label: "电子渠道门店类型：",
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _eleChannelNodeTypes = {
    type: "input",
    name: "eleChannelNodeTypes",
    label: "其他补充：",
    width: 380,
    offsetTop: 10,
    hidden:true,
    maxLength:512
};


var _channelNodeRevertPlatform = {
    type: "hidden",
    name: "channelNodeRevertPlatform",
    label: "渠道门店归属网络交易平台：",
    width: 140,
    offsetTop: 10
};
var _channelNodeRevertPlatform0 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform0",
    label: "企业自建",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform1 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform1",
    label: "淘宝",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform2 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform2",
    label: "天猫",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform3 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform3",
    label: "京东",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform4 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform4",
    label: "拼多多",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform5 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform5",
    label: "抖音",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform6 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform6",
    label: "快手",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform7 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform7",
    label: "微信",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform8 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform8",
    label: "支付宝",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform9 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform9",
    label: "淘特",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform10 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform10",
    label: "其他",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform11 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform11",
    label: "今日头条",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform12 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform12",
    label: "百度营销（信息流）",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform13 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform13",
    label: "广点通（信息流）",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform14 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform14",
    label: "巨量引擎（信息流）",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform15 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform15",
    label: "磁力引擎（信息流）",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform16 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform16",
    label: "阿里超级汇川（信息流）",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform18 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform18",
    label: "垂类",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatform19 = {
    type: "checkbox",
    name: "channelNodeRevertPlatform19",
    label: "小红书",
    offsetTop: 10,
    labelWidth: 55
};
var _channelNodeRevertPlatforms = {
    type: "input",
    name: "channelNodeRevertPlatforms",
    label: "其他补充：",
    width: 240,
    offsetTop: 10,
    hidden:true,
    maxLength:512
};


var _manageScopeLabel = {
    type: "label",
    name: "manageScopeLabel",
    label: "营业范围："
};
var _manageScopeLabel1 = {
    type: "label",
    name: "manageScopeLabel1",
    offsetLeft : 10,
    label: "查询服务"
};
var _manageScopeLabel2 = {
    type: "label",
    name: "manageScopeLabel2",
    offsetLeft : 10,
    label: "充值服务"
};
var _manageScopeLabel3 = {
    type: "label",
    name: "manageScopeLabel3",
    offsetLeft : 10,
    label: "服务办理"
};
var _manageScopeLabel4 = {
    type: "label",
    name: "manageScopeLabel4",
    offsetLeft : 10,
    label: "业务办理"
};
var _manageScopeLabel5 = {
    type: "label",
    name: "manageScopeLabel5",
    offsetLeft : 10,
    label: "实物销售"
};
var _manageScopeLabel6 = {
    type: "label",
    name: "manageScopeLabel6",
    offsetLeft : 10,
    label: "宣传类"
};
var _manageScope = {
    type: "hidden",
    name: "manageScope",
    label: "营业范围：",
    width: 140,
    offsetTop: 10
};
var _manageScope1 = {
    type: "checkbox",
    name: "manageScope1",
    label: "话费查询",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope2 = {
    type: "checkbox",
    name: "manageScope2",
    label: "积分查询",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope3 = {
    type: "checkbox",
    name: "manageScope3",
    label: "流量查询",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope4 = {
    type: "checkbox",
    name: "manageScope4",
    label: "其他查询",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope5 = {
    type: "checkbox",
    name: "manageScope5",
    label: "充值缴费",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope6 = {
    type: "checkbox",
    name: "manageScope6",
    label: "流量充值",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope7 = {
    type: "checkbox",
    name: "manageScope7",
    label: "入网",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope8 = {
    type: "checkbox",
    name: "manageScope8",
    label: "过户",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope9 = {
    type: "checkbox",
    name: "manageScope9",
    label: "资料完善",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope10 = {
    type: "checkbox",
    name: "manageScope10",
    label: "改资料",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope11 = {
    type: "checkbox",
    name: "manageScope11",
    label: "密码重置",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope12 = {
    type: "checkbox",
    name: "manageScope12",
    label: "补卡",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope13 = {
    type: "checkbox",
    name: "manageScope13",
    label: "销户",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope14 = {
    type: "checkbox",
    name: "manageScope14",
    label: "销户重开",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope15 = {
    type: "checkbox",
    name: "manageScope15",
    label: "强制复机",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope16 = {
    type: "checkbox",
    name: "manageScope16",
    label: "销户转账",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope17 = {
    type: "checkbox",
    name: "manageScope17",
    label: "销户取现",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope18 = {
    type: "checkbox",
    name: "manageScope18",
    label: "黑名单出库",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope19 = {
    type: "checkbox",
    name: "manageScope19",
    label: "退预交款",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope20 = {
    type: "checkbox",
    name: "manageScope20",
    label: "集团V网关闭",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope21 = {
    type: "checkbox",
    name: "manageScope21",
    label: "8元套餐",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope22 = {
    type: "checkbox",
    name: "manageScope22",
    label: "小额充值卡销售",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope23 = {
    type: "checkbox",
    name: "manageScope23",
    label: "托收取消",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope24 = {
    type: "checkbox",
    name: "manageScope24",
    label: "跨省换卡、跨区密码重置、跨省销户",
    offsetTop: 10,
    labelWidth: 55
};

var _manageScope25 = {
    type: "checkbox",
    name: "manageScope25",
    label: "打印服务",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope26 = {
    type: "checkbox",
    name: "manageScope26",
    label: "解合约",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope27 = {
    type: "checkbox",
    name: "manageScope27",
    label: "降档",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope28 = {
    type: "checkbox",
    name: "manageScope28",
    label: "携号转网（转出）",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope29 = {
    type: "checkbox",
    name: "manageScope29",
    label: "携号转网（转入）",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope30 = {
    type: "checkbox",
    name: "manageScope30",
    label: "服务办理其他",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope31 = {
    type: "checkbox",
    name: "manageScope31",
    label: "号卡",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope32 = {
    type: "checkbox",
    name: "manageScope32",
    label: "固话",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope33 = {
    type: "checkbox",
    name: "manageScope33",
    label: "宽带",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope34 = {
    type: "checkbox",
    name: "manageScope34",
    label: "5G套包",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope35 = {
    type: "checkbox",
    name: "manageScope35",
    label: "权益",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope36 = {
    type: "checkbox",
    name: "manageScope36",
    label: "物联网卡",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope37 = {
    type: "checkbox",
    name: "manageScope37",
    label: "业务办理其他",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope38 = {
    type: "checkbox",
    name: "manageScope38",
    label: "终端销售（不含接机维修和以旧换新）",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope39 = {
    type: "checkbox",
    name: "manageScope39",
    label: "终端销售(含以旧换新)",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope40 = {
    type: "checkbox",
    name: "manageScope40",
    label: "终端销售(含接机维修）",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope41 = {
    type: "checkbox",
    name: "manageScope41",
    label: "终端销售(含接机维修和以旧换新）",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope42 = {
    type: "checkbox",
    name: "manageScope42",
    label: "儿童手表",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope43 = {
    type: "checkbox",
    name: "manageScope43",
    label: "智能家居",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope44 = {
    type: "checkbox",
    name: "manageScope44",
    label: "智能网关",
    offsetTop: 10,
    labelWidth: 55
};

var _manageScope45 = {
    type: "checkbox",
    name: "manageScope45",
    label: "其他终端（PC、平板、学习机）",
    offsetTop: 10,
    labelWidth: 55
};

var _manageScope46 = {
    type: "checkbox",
    name: "manageScope46",
    label: "产品宣传",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope47 = {
    type: "checkbox",
    name: "manageScope47",
    label: "政企业务",
    offsetTop: 10,
    labelWidth: 55
};
var _manageScope99 = {
    type: "checkbox",
    name: "manageScope99",
    label: "其他",
    offsetTop: 10,
    labelWidth: 20
};
var _manageScopes = {
    type: "input",
    name: "manageScopes",
    label: "营业范围(其他补充)：",
    width: 400,
    offsetTop: 10,
    hidden:true,
    maxLength:512
};
var _geographicalPositionType={
    type: "combo",
    name: "geographicalPositionType",
    label: "商圈属性：",
    readonly: true,
    required:true,
    width: 140,
    offsetTop: 10
};
var _isHundredPlan={
    type: "combo",
    name: "isHundredPlan",
    label: "是否百计划(精兵营)店面：",
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _isThousandPlan={
    type: "combo",
    name: "isThousandPlan",
    label: "是否千计划(先锋店)店面：",
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _isAllianceChannel={
    type: "combo",
    name: "isAllianceChannel",
    label: "是否泛全联盟渠道(数智生活店)：",
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _terminalSalesArea = {
    type: "input",
    name: "terminalSalesArea",
    label: "终端销售面积(平米)：",
    width: 140,
    offsetTop: 10,
    validate: "checkIsDigitalAndLength"
};

var _isAllianceImageReplacement = {
    type: "combo",
    name: "isAllianceImageReplacement",
    label: "是否完成泛全联盟形象换新：",
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _isAlliancePlateformAccess = {
    type: "combo",
    name: "isAlliancePlateformAccess",
    label: "是否完成泛全联盟云平台系统接入：",
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _isTerminalAgreement = {
    type: "combo",
    name: "isTerminalAgreement",
    label: "终端货源约定：",
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _cooperateEndDate = {
    type: "calendar",
    name: "cooperateEndDate",
    label: "合作协议到期时间：",
    required:true,
    width: 140,
    offsetTop: 10
};

var _terminalOwnership = {
    type: "combo",
    name: "terminalOwnership",
    label: "终端货物所有权：",
    readonly: true,
    width: 250,
    offsetTop: 10
};

var _salespersonSource = {
    type: "combo",
    name: "salespersonSource",
    label: "营业员来源：",
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _isTerminalCompany = {
    type: "combo",
    name: "isTerminalCompany",
    label: "合作方是否是终端公司：",
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _terminalCompanyExt = {
    type: "input",
    name: "terminalCompanyExt",
    label: "合作方是否是终端公司(其他补充)：",
    width: 140,
    disabled: true,
    offsetTop: 10
};

var _rentAmount={
    type: "combo",
    name: "rentAmount",
    label: "购置、租赁成本：",
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _subDivision={
    type: "combo",
    name: "subDivision",
    label: "渠道细分形态：",
    required:true,
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _terminalSalesFormat={
    type: "combo",
    name: "terminalSalesFormat",
    label: "终端销售业态：",
    hidden:true,
    readonly: true,
    width: 140,
    offsetTop: 10
};

var _isOnlineShop={
    type: "combo",
    name: "isOnlineShop",
    label: "是否线上开店：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};

var _vipNum={
    type: "input",
    name: "vipNum",
    label: "门店会员数量：",
    required: true,
    width: 140,
    validate:checkVipNum,
    offsetTop: 10
};
var _managementStyle={
    type: "combo",
    name: "managementStyle",
    label: "管理方式：",
    readonly: true,
    required:true,
    width: 140,
    offsetTop: 10
};
var _cooperationType={
    type: "combo",
    name: "cooperationType",
    label: "合作形式：",
    readonly: true,
    required:true,
    width: 140,
    offsetTop: 10
};
var _businessPatterns={
    type: "combo",
    name: "businessPatterns",
    label: "业务形态：",
    readonly: true,
    required:true,
    width: 140,
    offsetTop: 10
};
var _panChannelType={
    type: "combo",
    name: "panChannelType",
    label: "异业泛渠道类型：",
    readonly: true,
    required:true,
    width: 140,
    offsetTop: 10
};
var _panChannelExpansionMode={
    type: "combo",
    name: "panChannelExpansionMode",
    label: "异业泛渠道拓展方式：",
    readonly: true,
    required:true,
    width: 140,
    offsetTop: 10
};
var _industryPatterns={
    type: "combo",
    name: "industryPatterns",
    label: "行业形态：",
    readonly: true,
    required:true,
    width: 140,
    offsetTop: 10
};
var _industryPatternsAttrs={
    type: "input",
    name: "industryPatternsAttrs",
    label: "行业形态(属性)：",
    readonly: true,
    hidden:true,
    width: 140,
    offsetTop: 10
};
var _industryPatternsAttrsA1={
    type: "checkbox",
    name: "industryPatternsAttrsA1",
    label: "大型商场(商场)：",
    offsetTop: 10,
    offsetLeft: 50,
    labelWidth: 100
};
var _industryPatternsAttrsA3={
    type: "checkbox",
    name: "industryPatternsAttrsA3",
    label: "便利店(百货)：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsA2={
    type: "checkbox",
    name: "industryPatternsAttrsA2",
    label: "连锁超市(超市)：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsA4={
    type: "checkbox",
    name: "industryPatternsAttrsA4",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};

var _industryPatternsAttrsB1={
    type: "checkbox",
    name: "industryPatternsAttrsB1",
    label: "家电专卖店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsB2={
    type: "checkbox",
    name: "industryPatternsAttrsB2",
    label: "智能终端：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsB3={
    type: "checkbox",
    name: "industryPatternsAttrsB3",
    label: "电动车：",
    offsetTop: 10,
    labelWidth: 100
};

var _industryPatternsAttrsB4={
    type: "checkbox",
    name: "industryPatternsAttrsB4",
    label: "家电维修点：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsB5={
    type: "checkbox",
    name: "industryPatternsAttrsB5",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsC1={
    type: "checkbox",
    name: "industryPatternsAttrsC1",
    label: "电影院：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsC2={
    type: "checkbox",
    name: "industryPatternsAttrsC2",
    label: "网吧：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsC3={
    type: "checkbox",
    name: "industryPatternsAttrsC3",
    label: "棋牌室：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsC4={
    type: "checkbox",
    name: "industryPatternsAttrsC4",
    label: "洗浴：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsC5={
    type: "checkbox",
    name: "industryPatternsAttrsC5",
    label: "KTV：",
    offsetTop: 10,
    labelWidth: 100
};

var _industryPatternsAttrsC6={
    type: "checkbox",
    name: "industryPatternsAttrsC6",
    label: "按摩：",
    offsetTop: 10,
    labelWidth: 100
};

var _industryPatternsAttrsC7={
    type: "checkbox",
    name: "industryPatternsAttrsC7",
    label: "茶馆：",
    offsetTop: 10,
    labelWidth: 100
};

var _industryPatternsAttrsC8={
    type: "checkbox",
    name: "industryPatternsAttrsC8",
    label: "酒吧：",
    offsetTop: 10,
    labelWidth: 100
};

var _industryPatternsAttrsC9={
    type: "checkbox",
    name: "industryPatternsAttrsC9",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsD1={
    type: "checkbox",
    name: "industryPatternsAttrsD1",
    label: "商圈美食：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsD2={
    type: "checkbox",
    name: "industryPatternsAttrsD2",
    label: "网红店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsD3={
    type: "checkbox",
    name: "industryPatternsAttrsD3",
    label: "小吃店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsD4={
    type: "checkbox",
    name: "industryPatternsAttrsD4",
    label: "烧烤店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsD5={
    type: "checkbox",
    name: "industryPatternsAttrsD5",
    label: "面馆：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsD6={
    type: "checkbox",
    name: "industryPatternsAttrsD6",
    label: "家常菜馆：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsD7={
    type: "checkbox",
    name: "industryPatternsAttrsD7",
    label: "火锅店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsD8={
    type: "checkbox",
    name: "industryPatternsAttrsD8",
    label: "早餐店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsD9={
    type: "checkbox",
    name: "industryPatternsAttrsD9",
    label: "本地老字号餐馆：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsD10={
    type: "checkbox",
    name: "industryPatternsAttrsD10",
    label: "特色餐吧：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsD11={
    type: "checkbox",
    name: "industryPatternsAttrsD11",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsE1={
    type: "checkbox",
    name: "industryPatternsAttrsE1",
    label: "洗车：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsE2={
    type: "checkbox",
    name: "industryPatternsAttrsE2",
    label: "4S店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsE3={
    type: "checkbox",
    name: "industryPatternsAttrsE3",
    label: "维修：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsE4={
    type: "checkbox",
    name: "industryPatternsAttrsE4",
    label: "轮胎店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsE5={
    type: "checkbox",
    name: "industryPatternsAttrsE5",
    label: "美容装潢店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsE6={
    type: "checkbox",
    name: "industryPatternsAttrsE6",
    label: "升级改装店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsE7={
    type: "checkbox",
    name: "industryPatternsAttrsE7",
    label: "加油站：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsE8={
    type: "checkbox",
    name: "industryPatternsAttrsE8",
    label: "审验场：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsE9={
    type: "checkbox",
    name: "industryPatternsAttrsE9",
    label: "二手车展厅：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsE10={
    type: "checkbox",
    name: "industryPatternsAttrsE10",
    label: "租车：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsE11={
    type: "checkbox",
    name: "industryPatternsAttrsE11",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsF1={
    type: "checkbox",
    name: "industryPatternsAttrsF1",
    label: "快递：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsF2={
    type: "checkbox",
    name: "industryPatternsAttrsF2",
    label: "外卖：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsF3={
    type: "checkbox",
    name: "industryPatternsAttrsF3",
    label: "市内货运：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsF4={
    type: "checkbox",
    name: "industryPatternsAttrsF4",
    label: "出租车：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsF5={
    type: "checkbox",
    name: "industryPatternsAttrsF5",
    label: "网约车：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsF6={
    type: "checkbox",
    name: "industryPatternsAttrsF6",
    label: "同城闪送：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsF7={
    type: "checkbox",
    name: "industryPatternsAttrsF7",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG1={
    type: "checkbox",
    name: "industryPatternsAttrsG1",
    label: "水果店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG2={
    type: "checkbox",
    name: "industryPatternsAttrsG2",
    label: "美发美容：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG3={
    type: "checkbox",
    name: "industryPatternsAttrsG3",
    label: "蛋糕店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG4={
    type: "checkbox",
    name: "industryPatternsAttrsG4",
    label: "物业：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG5={
    type: "checkbox",
    name: "industryPatternsAttrsG5",
    label: "体检中心：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG6={
    type: "checkbox",
    name: "industryPatternsAttrsG6",
    label: "鲜花店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG7={
    type: "checkbox",
    name: "industryPatternsAttrsG7",
    label: "眼镜店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG8={
    type: "checkbox",
    name: "industryPatternsAttrsG8",
    label: "甜品奶茶店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG9={
    type: "checkbox",
    name: "industryPatternsAttrsG9",
    label: "咖啡店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG10={
    type: "checkbox",
    name: "industryPatternsAttrsG10",
    label: "药店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG11={
    type: "checkbox",
    name: "industryPatternsAttrsG11",
    label: "宠物店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG12={
    type: "checkbox",
    name: "industryPatternsAttrsG12",
    label: "房产中介：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG13={
    type: "checkbox",
    name: "industryPatternsAttrsG13",
    label: "养老服务机构：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG14={
    type: "checkbox",
    name: "industryPatternsAttrsG14",
    label: "家装：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG15={
    type: "checkbox",
    name: "industryPatternsAttrsG15",
    label: "服装店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG16={
    type: "checkbox",
    name: "industryPatternsAttrsG16",
    label: "干洗店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsG17={
    type: "checkbox",
    name: "industryPatternsAttrsG17",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsH1={
    type: "checkbox",
    name: "industryPatternsAttrsH1",
    label: "APP：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsH2={
    type: "checkbox",
    name: "industryPatternsAttrsH2",
    label: "论坛：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsH3={
    type: "checkbox",
    name: "industryPatternsAttrsH3",
    label: "公众号：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsH4={
    type: "checkbox",
    name: "industryPatternsAttrsH4",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsI1={
    type: "checkbox",
    name: "industryPatternsAttrsI1",
    label: "银行：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsI2={
    type: "checkbox",
    name: "industryPatternsAttrsI2",
    label: "证券：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsI3={
    type: "checkbox",
    name: "industryPatternsAttrsI3",
    label: "保险：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsI4={
    type: "checkbox",
    name: "industryPatternsAttrsI4",
    label: "信托：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsI5={
    type: "checkbox",
    name: "industryPatternsAttrsI5",
    label: "理财：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsI6={
    type: "checkbox",
    name: "industryPatternsAttrsI6",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsJ1={
    type: "checkbox",
    name: "industryPatternsAttrsJ1",
    label: "驾校：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsJ2={
    type: "checkbox",
    name: "industryPatternsAttrsJ2",
    label: "培训机构：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsJ3={
    type: "checkbox",
    name: "industryPatternsAttrsJ3",
    label: "高校联盟：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsJ4={
    type: "checkbox",
    name: "industryPatternsAttrsJ4",
    label: "早教机构：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsJ5={
    type: "checkbox",
    name: "industryPatternsAttrsJ5",
    label: "公立学校：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsJ6={
    type: "checkbox",
    name: "industryPatternsAttrsJ6",
    label: "老年大学：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsJ7={
    type: "checkbox",
    name: "industryPatternsAttrsJ7",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsK1={
    type: "checkbox",
    name: "industryPatternsAttrsK1",
    label: "体育场馆：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsK2={
    type: "checkbox",
    name: "industryPatternsAttrsK2",
    label: "游泳馆：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsK3={
    type: "checkbox",
    name: "industryPatternsAttrsK3",
    label: "滑雪场：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsK4={
    type: "checkbox",
    name: "industryPatternsAttrsK4",
    label: "健身房：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsK5={
    type: "checkbox",
    name: "industryPatternsAttrsK5",
    label: "户外拓展：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsK6={
    type: "checkbox",
    name: "industryPatternsAttrsK6",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsL1={
    type: "checkbox",
    name: "industryPatternsAttrsL1",
    label: "景区：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsL2={
    type: "checkbox",
    name: "industryPatternsAttrsL2",
    label: "民宿：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsL3={
    type: "checkbox",
    name: "industryPatternsAttrsL3",
    label: "游乐园/场：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsL4={
    type: "checkbox",
    name: "industryPatternsAttrsL4",
    label: "农家乐：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsL5={
    type: "checkbox",
    name: "industryPatternsAttrsL5",
    label: "动植物园：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsL6={
    type: "checkbox",
    name: "industryPatternsAttrsL6",
    label: "火车站：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsL7={
    type: "checkbox",
    name: "industryPatternsAttrsL7",
    label: "机场：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsL8={
    type: "checkbox",
    name: "industryPatternsAttrsL8",
    label: "长途车站：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsL9={
    type: "checkbox",
    name: "industryPatternsAttrsL9",
    label: "宾馆酒店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsL10={
    type: "checkbox",
    name: "industryPatternsAttrsL10",
    label: "其他：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsM1={
    type: "checkbox",
    name: "industryPatternsAttrsM1",
    label: "区域联盟：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsM2={
    type: "checkbox",
    name: "industryPatternsAttrsM2",
    label: "宠物店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsM3={
    type: "checkbox",
    name: "industryPatternsAttrsM3",
    label: "福彩：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsM4={
    type: "checkbox",
    name: "industryPatternsAttrsM4",
    label: "体彩：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsM5={
    type: "checkbox",
    name: "industryPatternsAttrsM5",
    label: "带投注机门店：",
    offsetTop: 10,
    labelWidth: 100
};
var _industryPatternsAttrsM6={
    type: "checkbox",
    name: "industryPatternsAttrsM6",
    label: "其他自定义：",
    offsetTop: 10,
    labelWidth: 100
};



var _orgName = {
    type: "input",
    name: "orgName",
    label: "实体对应关系：",
    width: 140,
    offsetTop: 10
};
var _totalFee = {
    type: "input",
    name: "totalFee",
    label: "保证金：",
    width: 140,
    offsetTop: 10
};

var _channelFamilyAgentAreainfos = {
    type: "hidden",
    name: "channelFamilyAgentAreainfos"
};
var _delFamilyAreaIdList = {
    type: "hidden",
    name: "delFamilyAreaIdList"
};
//新加入字段
var _procureOrderExt5 = {
    type: "input",
    name: "ext5",
    label: "采购订单号：",
    width: 600,
    offsetTop: 10,
    required: true,
    validate: "submitExt5"
};
//新加入字段
var _nodeUpdateExt6 = {
    type: "combo",
    name: "ext6",
    label: "是否属于变更营业厅：",
    readonly: true,
    width: 140,
    offsetTop: 10,
    required: true
};
var _procureOrderNameExt7 = {
    type: "input",
    name: "ext7",
    label: "原营业厅名称：",
    maxLength: 12,
    width: 140,
    offsetTop: 10,
    required: true
};

/**
 * 校验界面数字类型
 *
 * @param data
 *            数字
 * @param name
 *            当前控件名称
 * @returns {Boolean} 返回状态
 */
function checkIsDigitalAndLength(data, name) {
    data = data + "";
    var trueOrFalse = true;
    var d = /\d+/;
    if (!d.test(data)) {
        return false;
    }
    // 每月空选白卡申领额度
    if (name == "whiteCardLimit") {
        if (data.length > 4) {
            trueOrFalse = false;
        }
    }

    if(name=="terminalSalesArea") {
        //支持两位小数和0
        var reg = /^(([1-9][0-9]*)|0|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
        if (!reg.test(data)) {
            return false;
        }
    }
    // 营业厅编制数量
    if (name == "compileNum") {
        if (data.length > 4) {
            trueOrFalse = false;
        }
    }
    // 营业厅员工数量
    if (name == "staffNum") {
        if (data.length > 4) {
            trueOrFalse = false;
        }
    }
    // 其中A类员工数量
    if (name == "staffNumA") {
        if (data.length > 4) {
            trueOrFalse = false;
        }
    }
    // 其中C类员工数量
    if (name == "staffNumC") {
        if (data.length > 4) {
            trueOrFalse = false;
        }
    }
    // 物理台席数
    if (name == "physicsNum") {
        if (data.length > 8) {
            trueOrFalse = false;
        }
    }
    // 实际台席数
    if (name == "seatNum") {
        if (data.length > 8) {
            trueOrFalse = false;
        }
    }
    // 保安人数（位）
    if (name == "empSafeNums") {
        if (data.length > 10) {
            trueOrFalse = false;
        }
    }
    // 保洁人数（位）
    if (name == "empClearNums") {
        if (data.length > 10) {
            trueOrFalse = false;
        }
    }
    // 总面积
    if (name == "sumArea") {
        if (data.length > 12) {
            trueOrFalse = false;
        }
    }
    // 前台面积
    if (name == "frontUseArea") {
        if (data.length > 12) {
            trueOrFalse = false;
        }
    }
    // 租金
    if (name == "houseRentAmount") {
        if (data.length > 12) {
            trueOrFalse = false;
        }
    }
    // 购房金额
    if (name == "houseAmount") {
        if (data.length > 12) {
            trueOrFalse = false;
        }
    }
    // 设备投资总额
    if (name == "buildingAmount") {
        if (data.length > 12) {
            trueOrFalse = false;
        }
    }
    // 装修投资总额
    if (name == "eqmtAmount") {
        if (data.length > 12) {
            trueOrFalse = false;
        }
    }
    // 办公和营业家具累计投资总额
    if (name == "officesAmount") {
        if (data.length > 20) {
            trueOrFalse = false;
        }
    }
    // 月号码销量
    if (name == "simSaleMonthly") {
        if (data.length > 10) {
            trueOrFalse = false;
        }
    }

    // 月手机销量
    if (name == "cellSaleMonthly") {
        if (data.length > 10) {
            trueOrFalse = false;
        }
    }

    // 月3G/4G手机销
    if (name == "g3g4CellSaleMonthly") {
        if (data.length > 10) {
            trueOrFalse = false;
        }
    }
    // 月冲值卡额度
    if (name == "czcardLimit") {
        if (data.length > 10) {
            trueOrFalse = false;
        }
    }

    // 月有号卡额度
    if (name == "yhcardLimit") {
        if (data.length > 10) {
            trueOrFalse = false;
        }
    }
    // 营业面积
    if (name == "useArea") {
        if (data.length > 20) {
            trueOrFalse = false;
        }
    }
    return trueOrFalse;
};

/**
 * 校验界面数字类型
 *
 * @param data
 *            数字
 * @param name
 *            当前控件名称
 */
function submitEntityName(data, name) {
    if (data == "未知") {
        dhtmlx.alert({title: "警告", text: "名称不能输入(未知)", type: "alert-error"});
        return false;
    }
    if (data.length < 4) {
        dhtmlx.alert({title: "警告", text: "渠道名称不能小于4个字符", type: "alert-error"});
        return false;
    }
    if (data.indexOf(" ")>0){
        dhtmlx.alert({title: "警告", text: "网点名称包含空格，请重新输入", type: "alert-error"});
        return false;
    }
    return true;
};

function submitNodeAddr(data, name) {
    var reEs = /x/gi;
    var reEs1 = /address/gi;
    var reEs2 = /test/gi;
    var reEs3 = /undefined/gi;
    var reEse = reEs.exec(data);
    var reEse1 = reEs1.exec(data);
    var reEse2 = reEs2.exec(data);
    var reEse3 = reEs3.exec(data);
    if (data == "未知" || data == "不详" || data == "无") {
        dhtmlx.alert({title: "警告", text: "地址不能输入(未知,不详,无)", type: "alert-error"});
        return false;
    }
    if (data.length < 4) {
        dhtmlx.alert({title: "警告", text: "渠道名称不能小于4个字符", type: "alert-error"});
        return false;
    }
    if (null != reEse && reEse.length > 0) {
        dhtmlx.alert({title: "警告", text: "地址输入不合法", type: "alert-error"});
        return false;
    }
    if (null != reEse1 && reEse1.length > 0) {
        dhtmlx.alert({title: "警告", text: "地址输入不合法", type: "alert-error"});
        return false;
    }
    if (null != reEse2 && reEse2.length > 0) {
        dhtmlx.alert({title: "警告", text: "地址输入不合法", type: "alert-error"});
        return false;
    }
    if (null != reEse3 && reEse3.length > 0) {
        dhtmlx.alert({title: "警告", text: "地址输入不合法", type: "alert-error"});
        return false;
    }
    return true;
}

/** ************************************** */
/**新加入需求，新增：
 *    *************系统接入方式类型
 *  *************业务承载类型
 *  *************是否授权网点*/
/** ************************************** */

/** ***************手机专卖店和授权代理店 手机卖场*********************** */
/**系统接入方式类型*/
var _systemExt0 = {
    type: "combo",
    name: "systemExt0",
    label: "系统接入方式：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _systemExt1Type = {
    type: "hidden",
    name: "systemExt1Type",
    label: "系统接入方式：",
    width: 140,
    offsetTop: 10
};
var _YTJ = {
    type: "checkbox",
    name: "YTJ",
    label: "一体机接入：",
    offsetTop: 10,
    labelWidth: 105
};
var _PC = {
    type: "checkbox",
    name: "PC",
    label: "PC接入：",
    offsetTop: 10,
    labelWidth: 105
};
var _KXJ = {
    type: "checkbox",
    name: "KXJ",
    label: "空选机接入：",
    offsetTop: 10,
    labelWidth: 105
};
var _XYWD = {
    type: "checkbox",
    name: "XYWD",
    label: "小移微店：",
    offsetTop: 10,
    labelWidth: 105
};

/**业务承载类型*/
var _businessExt2 = {
    type: "combo",
    name: "businessExt2",
    label: "业务承载类型：",
    width: 140,
    offsetTop: 10,
    readonly: true,
    required: true
};
var _businessExt3Type = {
    type: "hidden",
    name: "businessExt3Type",
    label: "业务承载类型：",
    width: 140,
    offsetTop: 10
};
var _BKXK = {
    type: "checkbox",
    name: "BKXK",
    label: "白卡写卡：",
    offsetTop: 10,
    labelWidth: 105
};
var _SMDJ = {
    type: "checkbox",
    name: "SMDJ",
    label: "实名登记：",
    offsetTop: 10,
    labelWidth: 105
};
var _ZDXS = {
    type: "checkbox",
    name: "ZDXS",
    label: "终端销售：",
    offsetTop: 10,
    labelWidth: 105
};
var _KDYW = {
    type: "checkbox",
    name: "KDYW",
    label: "宽带业务：",
    offsetTop: 10,
    labelWidth: 105
};

//新加入
function submitExt5(data, name) {
    data = data + "";
    var trueOrFalse = true;
    var type = /^[0-9]*[1-9][0-9]*$/;
    if (data != null) {
        if (contains(data, "，", true)) {
            dhtmlx.alert({title: "警告", text: "采购订单号输入错误，出现中文符号，请确认！", type: "alert-error"});
            return false;
        } else {
            var arr = new Array;
            arr = data.split(",");
            for (var i = 0; i < arr.length; i++) {
                if (!type.test(arr[i])) {
                    dhtmlx.alert({title: "提示", text: "采购订单号输入不合法！", type: "alert-warning"});
                    return false;
                }
                if (arr[i].length > 20 || arr[i].length < 20) {
                    dhtmlx.alert({title: "警告", text: "单个采购订单号应该为20个字符", type: "alert-error"});
                    return false;
                }
            }
        }
    }
    return true;
};

/**
 * 判断 str 字符串中是否含有字符串 subStr
 * @param {} str 原字符串
 * @param {} subStr 要查找的字符串
 * @param {} isIgnoreCase 是否忽略大小写
 * @return {Boolean}
 */
function contains(str, subStr, isIgnoreCase) {

    if (isIgnoreCase) {
        // 忽略大小写
        str = str.toLowerCase();
        subStr = subStr.toLowerCase();
    }

    var startChar = subStr.substring(0, 1);
    var strLen = subStr.length;

    for (var j = 0; j < str.length - strLen + 1; j++) {
        if (str.charAt(j) == startChar) {
            /* 如果匹配起始字符,开始查找 */
            if (str.substring(j, j + strLen) == subStr) {
                /*如果从j开始的字符与 str 匹配 */
                return true;
            }
        }
    }
    return false;
};
/*合同信息
*
* */
var _contractNumber1 = {
    type: "input",
    name: "contractNumber1",
    label: "合同编号：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _contractMarkTarget = {
    type: "input",
    name: "contractMarkTarget",
    label: "合同名称：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _contractAmount = {
    type: "input",
    name: "contractAmount",
    label: "合同金额：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _contractBeginDate = {
    type: "input",
    name: "contractBeginDate",
    label: "合同开始：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _contractEndDate = {
    type: "input",
    name: "contractEndDate",
    label: "合同结束：",
    width: 140,
    offsetTop: 10,
    required: true
};
var _passOldMonth = {
    type: "input",
    name: "passOldMonth",
    label: "递延月份：",
    width: 140,
    offsetTop: 10,
    required: false,
    validate: "checkPassOldMonth"
};

function checkPassOldMonth(data) {
    data = data + "";
    var reg3 = /^\d*$/;
    if (reg3.test(data) || data == "0" || data == "") {
        return true;
    } else {
        dhtmlx.alert({title: "警告", text: "合同递延月必须为整数，请重新输入", type: "alert-error"});
        return false;
    }
}

function checkChannelEntityName(data){
    data = data + "";
    if (data.indexOf(" ")>0){
        dhtmlx.alert({title: "警告", text: "网点名称包含空格，请重新输入", type: "alert-error"});
        return false;
    }
}
function checkVipNum(data) {
    data = data + "";
    var reg3 = /^\d*$/;
    if (reg3.test(data) || data == "0") {
        return true;
    } else {
        dhtmlx.alert({title: "警告", text: "门店会员数量必须为整数，请重新输入", type: "alert-error"});
        return false;
    }
}

function checkPauseReason(data) {
    data = data + "";
    var reg = /[ \r\n\t]+/;
    if (reg.test(data)){
        dhtmlx.alert({title: "警告", text: "暂停（关闭）原因包含包括空格符等特殊字符，请重新输入", type: "alert-error"});
        return false;
    }else{
        return  true;
    }
}

var _amountIncludingTax = {
    type: "input",
    name: "amountIncludingTax",
    label: "合同含税总额：",
    width: 140,
    offsetTop: 10,
    required: false
};
var _amountExcludingTax = {
    type: "input",
    name: "amountExcludingTax",
    label: "合同不含税总额：",
    width: 140,
    offsetTop: 10,
    required: false
};
var _vendorCode = {
    type: "input",
    name: "vendorCode",
    label: "相对方编码：",
    width: 140,
    offsetTop: 10,
    required: false
};
var _vendorNameC = {
    type: "input",
    name: "vendorNameC",
    label: "相对方名称：",
    width: 140,
    offsetTop: 10,
    required: false
};
var _constractNewsConnection = {
    type: "button",
    name: "constractNewsConnection",
    value: "合作方合同信息关联",
    offsetLeft: 340
};

var _tradingType = {
    type: "combo",
    name: "tradingType",
    label: "商圈类型：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _geographyPosition = {
    type: "combo",
    name: "geographyPosition",
    label: "地理位置：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _signBoardType = {
    type: "combo",
    name: "signBoardType",
    label: "店招类型：",
    readonly: true,
    required: true,
    width: 140,
    offsetTop: 10
};
var _coBranding = {
    type: "input",
    name: "coBranding",
    label: "联合品牌：",
    width: 140,
    offsetTop: 10,
};

var _isSixCombinationStore = {
    type: "combo",
    name: "isSixCombinationStore",
    label: "是否符合2025年社区店六个结合运营要求",
    readonly: true,
    width: 140,
    offsetTop: 10
};
var _isAssignedDesignatedArea = {
    type: "combo",
    name: "isAssignedDesignatedArea",
    label: "是否与属地包片结合",
    readonly: true,
    width: 140,
    offsetTop: 10,
};